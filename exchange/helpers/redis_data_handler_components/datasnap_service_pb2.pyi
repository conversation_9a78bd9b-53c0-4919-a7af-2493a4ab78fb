"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import sys

if sys.version_info >= (3, 8):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class DataSnapRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SEGMENT_FIELD_NUMBER: builtins.int
    TIMESTAMP_FIELD_NUMBER: builtins.int
    ENCODING_FIELD_NUMBER: builtins.int
    segment: builtins.str
    timestamp: builtins.str
    encoding: builtins.str
    def __init__(
        self,
        *,
        segment: builtins.str = ...,
        timestamp: builtins.str = ...,
        encoding: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "encoding", b"encoding", "segment", b"segment", "timestamp", b"timestamp"
        ],
    ) -> None: ...

global___DataSnapRequest = DataSnapRequest

class TimeStampRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SEGMENT_FIELD_NUMBER: builtins.int
    segment: builtins.str
    def __init__(
        self,
        *,
        segment: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["segment", b"segment"]
    ) -> None: ...

global___TimeStampRequest = TimeStampRequest

class RepeatedArrayReply(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MESSAGE_FIELD_NUMBER: builtins.int
    @property
    def message(
        self,
    ) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[
        builtins.str
    ]: ...
    def __init__(
        self,
        *,
        message: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["message", b"message"]
    ) -> None: ...

global___RepeatedArrayReply = RepeatedArrayReply

class TimestampResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TIMESTAMP_FIELD_NUMBER: builtins.int
    timestamp: builtins.str
    def __init__(
        self,
        *,
        timestamp: builtins.str = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["timestamp", b"timestamp"]
    ) -> None: ...

global___TimestampResponse = TimestampResponse
