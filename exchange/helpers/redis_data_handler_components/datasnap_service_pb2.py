# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: datasnap_service.proto
"""Generated protocol buffer code."""

from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


DESCRIPTOR = _descriptor.FileDescriptor(
    name="datasnap_service.proto",
    package="datasnapservice",
    syntax="proto3",
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
    serialized_pb=b'\n\x16\x64\x61tasnap_service.proto\x12\x0f\x64\x61tasnapservice"G\n\x0f\x44\x61taSnapRequest\x12\x0f\n\x07segment\x18\x01 \x01(\t\x12\x11\n\ttimestamp\x18\x02 \x01(\t\x12\x10\n\x08\x65ncoding\x18\x03 \x01(\t"#\n\x10TimeStampRequest\x12\x0f\n\x07segment\x18\x01 \x01(\t"%\n\x12RepeatedArrayReply\x12\x0f\n\x07message\x18\x01 \x03(\t"&\n\x11TimestampResponse\x12\x11\n\ttimestamp\x18\x01 \x01(\t2\x97\x02\n\x08\x44\x61taSnap\x12S\n\x08get_data\x12 .datasnapservice.DataSnapRequest\x1a#.datasnapservice.RepeatedArrayReply"\x00\x12X\n\rget_tick_data\x12 .datasnapservice.DataSnapRequest\x1a#.datasnapservice.RepeatedArrayReply"\x00\x12\\\n\x11get_latest_second\x12!.datasnapservice.TimeStampRequest\x1a".datasnapservice.TimestampResponse"\x00\x62\x06proto3',
)


_DATASNAPREQUEST = _descriptor.Descriptor(
    name="DataSnapRequest",
    full_name="datasnapservice.DataSnapRequest",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="segment",
            full_name="datasnapservice.DataSnapRequest.segment",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="timestamp",
            full_name="datasnapservice.DataSnapRequest.timestamp",
            index=1,
            number=2,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.FieldDescriptor(
            name="encoding",
            full_name="datasnapservice.DataSnapRequest.encoding",
            index=2,
            number=3,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=43,
    serialized_end=114,
)


_TIMESTAMPREQUEST = _descriptor.Descriptor(
    name="TimeStampRequest",
    full_name="datasnapservice.TimeStampRequest",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="segment",
            full_name="datasnapservice.TimeStampRequest.segment",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=116,
    serialized_end=151,
)


_REPEATEDARRAYREPLY = _descriptor.Descriptor(
    name="RepeatedArrayReply",
    full_name="datasnapservice.RepeatedArrayReply",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="message",
            full_name="datasnapservice.RepeatedArrayReply.message",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=3,
            has_default_value=False,
            default_value=[],
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=153,
    serialized_end=190,
)


_TIMESTAMPRESPONSE = _descriptor.Descriptor(
    name="TimestampResponse",
    full_name="datasnapservice.TimestampResponse",
    filename=None,
    file=DESCRIPTOR,
    containing_type=None,
    create_key=_descriptor._internal_create_key,
    fields=[
        _descriptor.FieldDescriptor(
            name="timestamp",
            full_name="datasnapservice.TimestampResponse.timestamp",
            index=0,
            number=1,
            type=9,
            cpp_type=9,
            label=1,
            has_default_value=False,
            default_value=b"".decode("utf-8"),
            message_type=None,
            enum_type=None,
            containing_type=None,
            is_extension=False,
            extension_scope=None,
            serialized_options=None,
            file=DESCRIPTOR,
            create_key=_descriptor._internal_create_key,
        ),
    ],
    extensions=[],
    nested_types=[],
    enum_types=[],
    serialized_options=None,
    is_extendable=False,
    syntax="proto3",
    extension_ranges=[],
    oneofs=[],
    serialized_start=192,
    serialized_end=230,
)

DESCRIPTOR.message_types_by_name["DataSnapRequest"] = _DATASNAPREQUEST
DESCRIPTOR.message_types_by_name["TimeStampRequest"] = _TIMESTAMPREQUEST
DESCRIPTOR.message_types_by_name["RepeatedArrayReply"] = _REPEATEDARRAYREPLY
DESCRIPTOR.message_types_by_name["TimestampResponse"] = _TIMESTAMPRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

DataSnapRequest = _reflection.GeneratedProtocolMessageType(
    "DataSnapRequest",
    (_message.Message,),
    {
        "DESCRIPTOR": _DATASNAPREQUEST,
        "__module__": "datasnap_service_pb2",
        # @@protoc_insertion_point(class_scope:datasnapservice.DataSnapRequest)
    },
)
_sym_db.RegisterMessage(DataSnapRequest)

TimeStampRequest = _reflection.GeneratedProtocolMessageType(
    "TimeStampRequest",
    (_message.Message,),
    {
        "DESCRIPTOR": _TIMESTAMPREQUEST,
        "__module__": "datasnap_service_pb2",
        # @@protoc_insertion_point(class_scope:datasnapservice.TimeStampRequest)
    },
)
_sym_db.RegisterMessage(TimeStampRequest)

RepeatedArrayReply = _reflection.GeneratedProtocolMessageType(
    "RepeatedArrayReply",
    (_message.Message,),
    {
        "DESCRIPTOR": _REPEATEDARRAYREPLY,
        "__module__": "datasnap_service_pb2",
        # @@protoc_insertion_point(class_scope:datasnapservice.RepeatedArrayReply)
    },
)
_sym_db.RegisterMessage(RepeatedArrayReply)

TimestampResponse = _reflection.GeneratedProtocolMessageType(
    "TimestampResponse",
    (_message.Message,),
    {
        "DESCRIPTOR": _TIMESTAMPRESPONSE,
        "__module__": "datasnap_service_pb2",
        # @@protoc_insertion_point(class_scope:datasnapservice.TimestampResponse)
    },
)
_sym_db.RegisterMessage(TimestampResponse)


_DATASNAP = _descriptor.ServiceDescriptor(
    name="DataSnap",
    full_name="datasnapservice.DataSnap",
    file=DESCRIPTOR,
    index=0,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
    serialized_start=233,
    serialized_end=512,
    methods=[
        _descriptor.MethodDescriptor(
            name="get_data",
            full_name="datasnapservice.DataSnap.get_data",
            index=0,
            containing_service=None,
            input_type=_DATASNAPREQUEST,
            output_type=_REPEATEDARRAYREPLY,
            serialized_options=None,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.MethodDescriptor(
            name="get_tick_data",
            full_name="datasnapservice.DataSnap.get_tick_data",
            index=1,
            containing_service=None,
            input_type=_DATASNAPREQUEST,
            output_type=_REPEATEDARRAYREPLY,
            serialized_options=None,
            create_key=_descriptor._internal_create_key,
        ),
        _descriptor.MethodDescriptor(
            name="get_latest_second",
            full_name="datasnapservice.DataSnap.get_latest_second",
            index=2,
            containing_service=None,
            input_type=_TIMESTAMPREQUEST,
            output_type=_TIMESTAMPRESPONSE,
            serialized_options=None,
            create_key=_descriptor._internal_create_key,
        ),
    ],
)
_sym_db.RegisterServiceDescriptor(_DATASNAP)

DESCRIPTOR.services_by_name["DataSnap"] = _DATASNAP

# @@protoc_insertion_point(module_scope)
