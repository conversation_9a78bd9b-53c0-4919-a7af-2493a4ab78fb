"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import abc
from exchange.helpers.redis_data_handler_components import datasnap_service_pb2
import grpc

class DataSnapStub:
    def __init__(self, channel: grpc.Channel) -> None: ...
    get_data: grpc.UnaryUnaryMultiCallable[
        datasnap_service_pb2.DataSnapRequest,
        datasnap_service_pb2.RepeatedArrayReply,
    ]
    get_tick_data: grpc.UnaryUnaryMultiCallable[
        datasnap_service_pb2.DataSnapRequest,
        datasnap_service_pb2.RepeatedArrayReply,
    ]
    get_latest_second: grpc.UnaryUnaryMultiCallable[
        datasnap_service_pb2.TimeStampRequest,
        datasnap_service_pb2.TimestampResponse,
    ]

class DataSnapServicer(metaclass=abc.ABCMeta):
    @abc.abstractmethod
    def get_data(
        self,
        request: datasnap_service_pb2.DataSnapRequest,
        context: grpc.ServicerContext,
    ) -> datasnap_service_pb2.RepeatedArrayReply: ...
    @abc.abstractmethod
    def get_tick_data(
        self,
        request: datasnap_service_pb2.DataSnapRequest,
        context: grpc.ServicerContext,
    ) -> datasnap_service_pb2.RepeatedArrayReply: ...
    @abc.abstractmethod
    def get_latest_second(
        self,
        request: datasnap_service_pb2.TimeStampRequest,
        context: grpc.ServicerContext,
    ) -> datasnap_service_pb2.TimestampResponse: ...

def add_DataSnapServicer_to_server(
    servicer: DataSnapServicer, server: grpc.Server
) -> None: ...
