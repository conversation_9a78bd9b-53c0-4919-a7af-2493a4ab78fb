from typing import cast
import grpc
from exchange.helpers.configstore import DATASNAP_HOST, DATASNAP_PORT
from exchange.helpers.redis_data_handler_components import datasnap_service_pb2
from exchange.helpers.redis_data_handler_components import datasnap_service_pb2_grpc


class DataSnapClient:
    def __init__(self) -> None:
        """
        Initialize the gRPC datasnap client.
        """
        self.channel = grpc.insecure_channel(f"{DATASNAP_HOST}:{DATASNAP_PORT}")
        self.stub = datasnap_service_pb2_grpc.DataSnapStub(self.channel)

    def get_data(
        self, segment: str, timestamp: str, encoding: str = ""
    ) -> datasnap_service_pb2.RepeatedArrayReply:
        """
        Get data for a specific segment and timestamp.

        Args:
            segment (str): The market segment
            timestamp (str): The timestamp to query
            encoding (str): Optional encoding parameter

        Returns:
            datasnap_service_pb2.RepeatedArrayReply: Array of strings containing the data
        """
        request = datasnap_service_pb2.DataSnapRequest(
            segment=segment, timestamp=timestamp, encoding=encoding
        )
        return cast(
            datasnap_service_pb2.RepeatedArrayReply, self.stub.get_data(request)
        )

    def get_tick_data(
        self, segment: str, timestamp: str, encoding: str = ""
    ) -> datasnap_service_pb2.RepeatedArrayReply:
        """
        Get tick data for a specific segment and timestamp.

        Args:
            segment (str): The market segment
            timestamp (str): The timestamp to query
            encoding (str): Optional encoding parameter

        Returns:
            datasnap_service_pb2.RepeatedArrayReply: Array of strings containing the tick data
        """
        request = datasnap_service_pb2.DataSnapRequest(
            segment=segment, timestamp=timestamp, encoding=encoding
        )
        return cast(
            datasnap_service_pb2.RepeatedArrayReply, self.stub.get_tick_data(request)
        )

    def get_latest_second(self, segment: str) -> datasnap_service_pb2.TimestampResponse:
        """
        Get the latest available second for a market segment.

        Args:
            segment (str): The market segment

        Returns:
            datasnap_service_pb2.TimestampResponse: The latest timestamp available
        """
        request = datasnap_service_pb2.TimeStampRequest(segment=segment)
        return cast(
            datasnap_service_pb2.TimestampResponse, self.stub.get_latest_second(request)
        )

    def close(self) -> None:
        """Close the gRPC channel"""
        self.channel.close()
