import asyncio
import datetime
from typing import Dict, Optional, Sequence, <PERSON>ple
import pandas as pd
from redis import asyncio as aioredis
from exchange.helpers.configstore import REDIS_HOST, REDIS_PORT
from core.helpers.configstore import EXCHANGE_TYPE
from exchange.helpers.redis_data_handler_components.datasnap_client import (
    DataSnapClient,
)
import balte.balte_config
from balte.utility_inbuilt import balte_initializer


class RedisDataHandlerRunner:
    """
    A Redis client for handling real-time tick data storage and retrieval.

    This class provides functionality to connect to Redis, store tick data
    with balte_id to LTP (Last Traded Price) mappings, and continuously
    fetch and store market data.

    Attributes:
        redis_host (str): Redis server hostname from configuration
        redis_port (int): Redis server port from configuration
        redis_pool (aioredis.Redis): Redis connection pool for async operations
        datasnap_client (DataSnapClient): Client for fetching data from datasnap service
        is_market_closed (asyncio.Future): Flag to indicate if market is closed
        market_closing_time (pd.Timestamp): Timestamp of market closing time
    """

    def __init__(self) -> None:
        """
        Initialize the Redis data client.

        Sets up Redis connection parameters from configuration and
        initializes the Redis connection pool.
        """
        self.redis_host: str = REDIS_HOST
        self.redis_port: int = int(REDIS_PORT)
        self.redis_db: str = ""
        self.redis_client: Optional[aioredis.Redis] = None
        self.datasnap_client: DataSnapClient = DataSnapClient()
        self.is_market_closed: asyncio.Future = asyncio.Future()  # type: ignore
        # Initialize BaLTE config for MCX
        balte_initializer(EXCHANGE_TYPE.lower())
        self.market_closing_time: pd.Timestamp = (
            self.get_local_timestamp()
            .normalize()
            .replace(
                hour=balte.balte_config.MARKET_CLOSE_HOUR,
                minute=balte.balte_config.MARKET_CLOSE_MINUTE,
            )
        )
        # Cache to store contract name to balte_id mappings
        self.contract_to_balte_id_cache: Dict[str, int] = {}

    @staticmethod
    def get_local_timestamp() -> pd.Timestamp:
        """
        Function to get current timestamp in the timezone specified by BaLTE, but returned as a timezone-naive Timestamp object.
        (i.e. consistent with that timezone but without the actual tzinfo attribute)

        Returns:
            pd.Timestamp: current timestamp
        """
        return pd.Timestamp.now(tz=balte.balte_config.TIMEZONE).replace(tzinfo=None)

    @staticmethod
    def parse_contract(contract: str) -> Tuple[str, str, str, str]:
        """Returns symbol, expiry, option_type, strike of given contract.
            If some values are not available, empty string is returned

        Args:
            contract (`str`): Contract to be parsed (e.g. NIFTY31-Jul-2025CE24000)

        Returns:
            `str`, `str`, `str`, `str`: symbol, expiry, option_type, strike respectively
        """
        month_lis = [
            "-Jan-",
            "-Feb-",
            "-Mar-",
            "-Apr-",
            "-May-",
            "-Jun-",
            "-Jul-",
            "-Aug-",
            "-Sep-",
            "-Oct-",
            "-Nov-",
            "-Dec-",
        ]
        symbol = ""
        option_type = ""
        expiry = ""
        strike = ""
        for tmp in month_lis:
            if tmp in contract:
                idx = contract.index(tmp)
                symbol = contract[: idx - 2]
                expiry = contract[idx - 2 : idx + 9]
                option_type = contract[idx + 9 : idx + 11]
                strike = contract[idx + 11 :]
                break
        if symbol == "":
            symbol = contract

        return symbol, expiry, option_type, strike

    @staticmethod
    def contract_name_to_balte_id(universe: str, contract_name: str) -> int:
        """
        Build a numeric BaLTE ID from a contract string of the form:
            SYMBOL[DD-MMM-YYYY][PE|CE|FF][STRIKE]
        Contract names must have strike in paisa.
        Some opt universe store strike in rupees in balteid and some just store the last 2 digits of year in expiry date

        Args:
            universe (str): Name of the universe the contract belongs to
            contract_name (str): Name of the contract for which to get balte id

        Rules:
        - If no expiry → return just the underlying symbol ID.
        - If expiry but no option_type → type_code='2' (e.g. futures).
        - If option_type=='CE' → type_code='1'; if 'PE' → '0'.

        Examples:
        'NIFTY' → 1234
        'NIFTY30-Jun-2022' → 202206302234  (if ID=2234, type_code=2)
        'NIFTY30-Jun-2022CE16550' → 16550202206341234

        Returns:
            int: BaLTE ID of the contract
        """
        symbol, expiry, option_type, strike = RedisDataHandlerRunner.parse_contract(
            contract=contract_name
        )
        underlying: int = balte.balte_config.symbol_to_balte_id.get(symbol, -1)

        if not expiry:
            return underlying

        expiry_dt = datetime.datetime.strptime(expiry, "%d-%b-%Y")
        expiry_code = expiry_dt.strftime("%Y%m%d")

        type_code_map = {
            "": "2",
            "CE": "1",
            "PE": "0",
        }
        try:
            type_code = type_code_map[option_type]
        except KeyError:
            raise ValueError(f"Invalid option type: {option_type!r}")

        underlying_code = f"{underlying:04d}"

        if not option_type and not strike:
            return int(f"{expiry_code}{type_code}{underlying_code}")
        elif not strike:
            raise ValueError(f"CE or PE type contract has no strike: {contract_name!r}")

        if "opt" in universe:
            if universe in [
                "opt",
                "opt_onemin",
                "opt_bse",
                "opt_bse_onemin",
            ]:
                strike = str(int(strike) // 100)

            elif universe in ["optcom", "optcom_onemin"]:
                strike = str(int(strike) // 10)

            elif universe in [
                "optstk",
                "optstk_onemin",
                "optstk_bse",
                "optstk_bse_onemin",
            ]:
                expiry_code = expiry_code[2:]

            else:
                raise NotImplementedError(
                    "contract_name_to_balte_id method is not implemented for this opt universe"
                )

        id_str = f"{strike}{expiry_code}{type_code}{underlying_code}"
        return int(id_str)

    async def connect_to_redis(self) -> None:
        """Establishes a connection to the Redis server if not already connected."""
        if self.redis_client is None:
            self.redis_client = await aioredis.from_url(  # type: ignore
                f"redis://{REDIS_HOST}:{REDIS_PORT}", decode_responses=True
            )

    def get_balte_id(self, contract: str) -> int:
        """
        Get the balte_id for a given contract.

        First checks the cache for existing mapping, if not found then calculates
        and stores in cache for future use.

        Args:
            contract (str): The contract symbol

        Returns:
            int: The balte_id associated with the contract
        """
        if contract in self.contract_to_balte_id_cache:
            return self.contract_to_balte_id_cache[contract]

        _, expiry, option_type, _ = self.parse_contract(contract)

        if option_type != "":
            balte_id = self.contract_name_to_balte_id(
                universe="optcom", contract_name=contract
            )
        elif expiry != "":
            balte_id = self.contract_name_to_balte_id(
                universe="mcx_fut_near", contract_name=contract
            )
        else:
            balte_id = self.contract_name_to_balte_id(
                universe="mcx_spot", contract_name=contract
            )

        self.contract_to_balte_id_cache[contract] = balte_id
        return balte_id

    def clear_balte_id_cache(self) -> None:
        """
        Clear the contract to balte_id cache.

        This method can be used to clear the cache if needed.
        """
        self.contract_to_balte_id_cache.clear()

    async def store_balte_id_to_ltp_in_redis(self, tick_data: Sequence[str]) -> None:
        """
        Store balte_id to Last Traded Price (LTP) mappings in Redis.

        Processes tick data and stores each contract's LTP in Redis using
        the balte_id as the key. Operations are performed concurrently
        for better performance.

        Args:
            tick_data (list): List of tick data strings in format "id|field1|ltp|..."
                            where contract is second element and LTP is fourth element

        Raises:
            Exception: If Redis pool is not initialized

        Note:
            Assumes tick data format: "contract|field1|ltp|other_fields"
            All operations are executed concurrently using asyncio.gather()
        """
        if self.redis_client is None:
            raise Exception("Redis client is not initialized")

        async with self.redis_client.pipeline(transaction=False) as pipe:
            for tick in tick_data:
                tick = str(tick)
                contract = tick.split("|")[1]
                balte_id = self.get_balte_id(contract=contract)

                if balte_id == -1:
                    continue

                ltp = tick.split("|")[3]

                await pipe.set(str(balte_id), ltp)
            await pipe.execute()

    async def market_termination_checker(self) -> None:
        """Calculates the time remaining in market closing and sleeps for that time.

        This function calculates the time remaining in market closing and sleeps for that time.
        Post that, it sets a flag which signals other async processes to stop as well.
        """
        seconds_until_market_termination: int = max(
            0, (self.market_closing_time - self.get_local_timestamp()).seconds
        )
        await asyncio.sleep(seconds_until_market_termination)
        self.is_market_closed.set_result(True)

    async def fetch_and_store_data(self) -> None:
        """
        Continuously fetch market data and store it in Redis.

        Runs an infinite loop that:
        1. Gets the latest timestamp from the data source
        2. Fetches tick data for that timestamp
        3. Stores the tick data in Redis as ID-LTP mappings

        Args:
            get_latest_second (callable): Function that returns an object with
                                        a 'timestamp' attribute containing the latest second
            get_tick_data (callable): Function that takes a timestamp and returns
                                    an object with a 'message' attribute containing tick data

        Note:
            This method runs indefinitely until an exception occurs.
            Errors are caught and printed but the loop continues.
        """
        while not self.is_market_closed.done():
            try:
                await self.connect_to_redis()
                latest_timestamp = (
                    self.datasnap_client.get_latest_second(segment="MCX")
                ).timestamp
                tick_data = self.datasnap_client.get_tick_data(
                    segment="MCX", timestamp=latest_timestamp
                )
                await self.store_balte_id_to_ltp_in_redis(tick_data.message)
            except Exception as e:
                print(f"Error occurred: {e}")

        # Clear cache and close datasnap client after market close
        self.clear_balte_id_cache()
        self.datasnap_client.close()

    async def redis_data_client_runner(self) -> None:
        """
        Run the core event loop for the Redis data client.

        Starts the continuous data fetching and storage process by running
        the fetch_and_store_data method in the asyncio event loop.

        Note:
            This method blocks until the fetch_and_store_data loop is interrupted.
            Uses the current event loop to run the async operations.
        """
        await asyncio.gather(
            self.fetch_and_store_data(), self.market_termination_checker()
        )

    def run(self) -> None:
        """
        Starts the Redis data client.

        This method initializes the asyncio event loop and runs the
        redis_data_client_runner coroutine within it. It blocks until
        the event loop is stopped.
        """
        asyncio.run(self.redis_data_client_runner())


if __name__ == "__main__":
    redis_data_handler_runner = RedisDataHandlerRunner()
    redis_data_handler_runner.run()
